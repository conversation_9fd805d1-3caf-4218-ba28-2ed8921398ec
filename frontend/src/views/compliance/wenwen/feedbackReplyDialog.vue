<!--
  *@name feedbackReplyDialog.vue
  *<AUTHOR> Assistant
  *@date 2025/7/29
  *@description 反馈答复Dialog组件
-->
<template>
  <el-dialog
    title="我的反馈"
    :visible.sync="visible"
    v-if="visible"
    width="1500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="feedback-reply-dialog"
  >
    <div class="dialogBody">
      <!-- 搜索条件 -->
      <div class="search-form">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item>
            <el-input
                v-model="searchForm.questionKeyword"
                placeholder="请输入问题/回复关键字"
                clearable
                style="width: 200px;"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
                v-model="searchForm.feedbackKeyword"
                placeholder="请输入反馈问题关键字"
                clearable
                style="width: 200px;"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-date-picker
                v-model="searchForm.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
                style="width: 350px;"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-select
                v-model="searchForm.feedbackType"
                placeholder="反馈类型"
                clearable
                style="width: 120px;"
            >
              <el-option label="回答有误" value="1"></el-option>
              <el-option label="响应慢" value="2"></el-option>
              <el-option label="案例有误" value="3"></el-option>
              <el-option label="法规有误" value="4"></el-option>
              <el-option label="其他" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <el-table
            :data="tableData"
            v-loading="loading"
            border
            style="width: 100%"
            :header-cell-style="{ background: '#E8F2FC' }"
        >
          <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
          <el-table-column prop="question" label="反馈问题" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="ellipsis-container">{{ scope.row.question }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="feedbackTypeName" label="反馈类型" width="100" align="center"></el-table-column>
          <el-table-column prop="createTime" label="首次提问时间" width="150" align="center"></el-table-column>
          <el-table-column prop="feedbackContent" label="最新反馈" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="ellipsis-container">{{ scope.row.feedbackContent }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="newFeedbackTime" label="最新反馈时间" width="150" align="center"></el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button
                  type="text"
                  size="small"
                  @click="handleViewDetail(scope.row)"
                  style="color: #1890ff;"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
        ></el-pagination>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { _getFeedbackReplyList } from "@/api/chat";
import { pickerOptions } from "@/utils/index";

export default {
  name: "feedbackReplyDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {
        questionKeyword: '',
        feedbackKeyword: '',
        timeRange: [],
        feedbackType: ''
      },
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      pickerOptions: pickerOptions
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.loadData();
      }
    }
  },
  methods: {
    // 加载数据
    loadData() {
      this.loading = true;
      const params = {
        pageSize: this.pagination.pageSize,
        startRow: (this.pagination.currentPage - 1) * this.pagination.pageSize,
        questionKeyword: this.searchForm.questionKeyword,
        feedbackKeyword: this.searchForm.feedbackKeyword,
        feedbackType: this.searchForm.feedbackType,
        startTime: this.searchForm.timeRange && this.searchForm.timeRange.length > 0 ? this.searchForm.timeRange[0] : '',
        endTime: this.searchForm.timeRange && this.searchForm.timeRange.length > 0 ? this.searchForm.timeRange[1] : ''
      };

      _getFeedbackReplyList(params).then(res => {
        if (res.data.success) {
          this.tableData = res.data.result.list || [];
          this.pagination.total = res.data.result.total || 0;
          
          // 处理反馈类型显示名称
          this.tableData.forEach(item => {
            switch(item.feedbackType) {
              case '1':
                item.feedbackTypeName = '回答有误';
                break;
              case '2':
                item.feedbackTypeName = '响应慢';
                break;
              case '3':
                item.feedbackTypeName = '案例有误';
                break;
              case '4':
                item.feedbackTypeName = '法规有误';
                break;
              case '0':
                item.feedbackTypeName = '其他';
                break;
              default:
                item.feedbackTypeName = '未知';
            }
          });
        } else {
          this.$message.error('数据加载失败');
        }
      }).catch(error => {
        this.$message.error('数据加载失败');
      }).finally(() => {
        this.loading = false;
      });
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1;
      this.loadData();
    },

    // 重置
    handleReset() {
      this.searchForm = {
        questionKeyword: '',
        feedbackKeyword: '',
        timeRange: [],
        feedbackType: ''
      };
      this.pagination.currentPage = 1;
      this.loadData();
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.loadData();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.loadData();
    },

    // 查看详情
    handleViewDetail(row) {
      // 这里可以实现查看详情的逻辑
      this.$message.info('查看详情功能待实现');
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close');
    }
  }
}
</script>

<style scoped lang="scss">
.feedback-reply-dialog {
  .dialogBody {
    height: 100%;
    .search-form {
      margin-bottom: 20px;
      padding: 20px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    .table-container {
      margin-bottom: 20px;

      .ellipsis-container {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 2;
        line-height: 1.5;
        max-height: 3em;
      }
    }

    .pagination-container {
      text-align: right;
      margin-bottom: 20px;
    }

    .dialog-footer {
      text-align: right;
    }
  }
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-table .cell {
  white-space: pre-wrap;
}
</style>
